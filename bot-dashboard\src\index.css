@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.dark .card {
  background-color: #1e293b;
  border-color: #334155;
}

.card-header {
  padding: 1.5rem;
  padding-bottom: 1rem;
}

.card-content {
  padding: 1.5rem;
  padding-top: 0;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #e5e7eb;
  color: #111827;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #d1d5db;
}

.dark .btn-secondary {
  background-color: #334155;
  color: #f1f5f9;
}

.dark .btn-secondary:hover {
  background-color: #475569;
}
