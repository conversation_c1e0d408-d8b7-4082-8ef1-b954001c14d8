import express from 'express'
import { WebSocketServer } from 'ws'
import http from 'http'
import cors from 'cors'

const app = express()
const server = http.createServer(app)

// Enable CORS for all routes
app.use(cors())
app.use(express.json())

// Store connected dashboard clients
const dashboardClients = new Set()

// Create WebSocket server
const wss = new WebSocketServer({ 
  server,
  path: '/ws'
})

// Handle WebSocket connections
wss.on('connection', (ws, req) => {
  console.log('New WebSocket connection established')
  
  // Add client to dashboard clients
  dashboardClients.add(ws)
  
  // Send welcome message
  ws.send(JSON.stringify({
    type: 'connection',
    message: 'Connected to bot dashboard server',
    timestamp: new Date().toISOString()
  }))
  
  // Handle messages from clients
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString())
      console.log('Received message:', message)
      
      // Handle different message types
      switch (message.type) {
        case 'ping':
          ws.send(JSON.stringify({
            type: 'pong',
            timestamp: new Date().toISOString()
          }))
          break
          
        case 'subscribe':
          // Client wants to subscribe to specific data types
          ws.subscriptions = message.subscriptions || ['all']
          ws.send(JSON.stringify({
            type: 'subscribed',
            subscriptions: ws.subscriptions,
            timestamp: new Date().toISOString()
          }))
          break
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  })
  
  // Handle client disconnect
  ws.on('close', () => {
    console.log('WebSocket connection closed')
    dashboardClients.delete(ws)
  })
  
  // Handle errors
  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
    dashboardClients.delete(ws)
  })
})

// Broadcast data to all connected dashboard clients
function broadcastToDashboard(data) {
  const message = JSON.stringify({
    ...data,
    timestamp: new Date().toISOString()
  })
  
  dashboardClients.forEach(client => {
    if (client.readyState === client.OPEN) {
      // Check if client is subscribed to this data type
      if (!client.subscriptions || 
          client.subscriptions.includes('all') || 
          client.subscriptions.includes(data.type)) {
        client.send(message)
      }
    }
  })
  
  console.log(`Broadcasted ${data.type} data to ${dashboardClients.size} clients`)
}

// WEBHOOK ENDPOINTS - Bot sẽ POST JSON data vào các endpoints này

// Webhook để nhận dữ liệu money rankings từ bot
app.post('/webhook/money-rankings', (req, res) => {
  try {
    console.log('📊 Received money rankings webhook:', req.body)

    // Validate JSON structure
    const data = req.body
    if (!data || typeof data !== 'object') {
      return res.status(400).json({
        error: 'Invalid JSON data',
        message: 'Expected JSON object'
      })
    }

    // Flexible data handling - bot có thể gửi nhiều format khác nhau
    let rankings = []

    if (Array.isArray(data)) {
      // Nếu bot gửi array trực tiếp
      rankings = data
    } else if (data.rankings && Array.isArray(data.rankings)) {
      // Nếu bot gửi {rankings: [...]}
      rankings = data.rankings
    } else if (data.data && Array.isArray(data.data)) {
      // Nếu bot gửi {data: [...]}
      rankings = data.data
    } else if (data.users && Array.isArray(data.users)) {
      // Nếu bot gửi {users: [...]}
      rankings = data.users
    } else {
      // Thử convert object thành array
      rankings = Object.values(data).filter(item =>
        item && typeof item === 'object' && (item.name || item.username || item.user)
      )
    }

    // Transform data to standard format nếu cần
    const standardizedRankings = rankings.map((item, index) => ({
      rank: item.rank || index + 1,
      id: item.id || item.user_id || item.userId || index + 1,
      name: item.name || item.username || item.user || `User ${index + 1}`,
      avatar: item.avatar || item.profile_image || item.image || `https://api.dicebear.com/7.x/avataaars/svg?seed=${item.name || index}`,
      earnings: item.earnings || item.money || item.balance || item.total || 0,
      change: item.change || item.diff || item.delta || 0
    }))

    // Broadcast to dashboard clients
    broadcastToDashboard({
      type: 'money_rankings',
      data: standardizedRankings
    })

    res.json({
      success: true,
      message: 'Money rankings updated successfully',
      processed: standardizedRankings.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error processing money rankings webhook:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    })
  }
})

// Webhook để nhận dữ liệu online users từ bot
app.post('/webhook/online-users', (req, res) => {
  try {
    console.log('👥 Received online users webhook:', req.body)

    const data = req.body
    if (!data || typeof data !== 'object') {
      return res.status(400).json({
        error: 'Invalid JSON data',
        message: 'Expected JSON object'
      })
    }

    // Flexible data handling
    let users = []

    if (Array.isArray(data)) {
      users = data
    } else if (data.users && Array.isArray(data.users)) {
      users = data.users
    } else if (data.online && Array.isArray(data.online)) {
      users = data.online
    } else if (data.data && Array.isArray(data.data)) {
      users = data.data
    } else {
      users = Object.values(data).filter(item =>
        item && typeof item === 'object' && (item.name || item.username || item.user)
      )
    }

    // Transform to standard format
    const standardizedUsers = users.map((item, index) => ({
      rank: item.rank || index + 1,
      id: item.id || item.user_id || item.userId || index + 1,
      name: item.name || item.username || item.user || `User ${index + 1}`,
      avatar: item.avatar || item.profile_image || item.image || `https://api.dicebear.com/7.x/avataaars/svg?seed=${item.name || index}`,
      onlineTime: item.onlineTime || item.online_time || item.time || item.minutes || 0,
      status: item.status || (item.online ? 'online' : 'offline') || 'online',
      activity: item.activity || item.action || item.doing || 'Active'
    }))

    broadcastToDashboard({
      type: 'online_users',
      data: standardizedUsers
    })

    res.json({
      success: true,
      message: 'Online users updated successfully',
      processed: standardizedUsers.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error processing online users webhook:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    })
  }
})

// Webhook để nhận dữ liệu coin holdings từ bot
app.post('/webhook/coin-holdings', (req, res) => {
  try {
    console.log('🪙 Received coin holdings webhook:', req.body)

    const data = req.body
    if (!data || typeof data !== 'object') {
      return res.status(400).json({
        error: 'Invalid JSON data',
        message: 'Expected JSON object'
      })
    }

    // Flexible data handling
    let holdings = []

    if (Array.isArray(data)) {
      holdings = data
    } else if (data.holdings && Array.isArray(data.holdings)) {
      holdings = data.holdings
    } else if (data.coins && Array.isArray(data.coins)) {
      holdings = data.coins
    } else if (data.wallets && Array.isArray(data.wallets)) {
      holdings = data.wallets
    } else if (data.data && Array.isArray(data.data)) {
      holdings = data.data
    } else {
      holdings = Object.values(data).filter(item =>
        item && typeof item === 'object' && (item.name || item.username || item.user)
      )
    }

    // Transform to standard format
    const standardizedHoldings = holdings.map((item, index) => {
      // Handle different coin data formats
      let coinHoldings = {}
      if (item.holdings && typeof item.holdings === 'object') {
        coinHoldings = item.holdings
      } else if (item.coins && typeof item.coins === 'object') {
        coinHoldings = item.coins
      } else if (item.wallet && typeof item.wallet === 'object') {
        coinHoldings = item.wallet
      } else {
        // Extract coin amounts from item properties
        const coinKeys = ['BTC', 'ETH', 'USDT', 'BNB', 'ADA', 'DOT', 'LINK', 'UNI']
        coinKeys.forEach(coin => {
          if (item[coin] !== undefined) {
            coinHoldings[coin] = item[coin]
          }
        })
      }

      // Calculate total value (simplified)
      const totalValue = item.totalValue || item.total_value || item.value ||
        Object.values(coinHoldings).reduce((sum, amount) => sum + (amount * 1000), 0) // Simplified calculation

      return {
        rank: item.rank || index + 1,
        id: item.id || item.user_id || item.userId || index + 1,
        name: item.name || item.username || item.user || `User ${index + 1}`,
        avatar: item.avatar || item.profile_image || item.image || `https://api.dicebear.com/7.x/avataaars/svg?seed=${item.name || index}`,
        holdings: coinHoldings,
        totalValue: totalValue,
        topCoin: item.topCoin || item.top_coin || Object.keys(coinHoldings)[0] || 'BTC'
      }
    })

    broadcastToDashboard({
      type: 'coin_holdings',
      data: standardizedHoldings
    })

    res.json({
      success: true,
      message: 'Coin holdings updated successfully',
      processed: standardizedHoldings.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error processing coin holdings webhook:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    })
  }
})

// Universal webhook - bot có thể gửi tất cả data vào 1 endpoint
app.post('/webhook/update', (req, res) => {
  try {
    console.log('🔄 Received universal webhook update:', req.body)

    const data = req.body
    if (!data || typeof data !== 'object') {
      return res.status(400).json({
        error: 'Invalid JSON data',
        message: 'Expected JSON object'
      })
    }

    const results = []

    // Check for money rankings data
    if (data.money_rankings || data.moneyRankings || data.earnings) {
      const rankings = data.money_rankings || data.moneyRankings || data.earnings
      if (Array.isArray(rankings)) {
        broadcastToDashboard({
          type: 'money_rankings',
          data: rankings
        })
        results.push('money_rankings updated')
      }
    }

    // Check for online users data
    if (data.online_users || data.onlineUsers || data.users) {
      const users = data.online_users || data.onlineUsers || data.users
      if (Array.isArray(users)) {
        broadcastToDashboard({
          type: 'online_users',
          data: users
        })
        results.push('online_users updated')
      }
    }

    // Check for coin holdings data
    if (data.coin_holdings || data.coinHoldings || data.holdings) {
      const holdings = data.coin_holdings || data.coinHoldings || data.holdings
      if (Array.isArray(holdings)) {
        broadcastToDashboard({
          type: 'coin_holdings',
          data: holdings
        })
        results.push('coin_holdings updated')
      }
    }

    res.json({
      success: true,
      message: 'Dashboard updated successfully',
      updated: results,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error processing universal webhook:', error)
    res.status(500).json({
      error: 'Internal server error',
      message: error.message
    })
  }
})

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    clients: dashboardClients.size,
    timestamp: new Date().toISOString()
  })
})

// Get current connection status
app.get('/api/status', (req, res) => {
  res.json({
    connectedClients: dashboardClients.size,
    uptime: process.uptime(),
    timestamp: new Date().toISOString()
  })
})

const PORT = process.env.PORT || 3001

server.listen(PORT, () => {
  console.log(`Bot Dashboard WebSocket Server running on port ${PORT}`)
  console.log(`WebSocket endpoint: ws://localhost:${PORT}/ws`)
  console.log(`API endpoints available at http://localhost:${PORT}/api/`)
})

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Shutting down server...')
  server.close(() => {
    console.log('Server closed')
    process.exit(0)
  })
})

export { broadcastToDashboard }
