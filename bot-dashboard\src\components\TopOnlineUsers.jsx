import { useState, useEffect } from 'react'
import { Users, Clock, Activity, AlertCircle, Loader2 } from 'lucide-react'
import { fetchTopOnlineUsers, formatTime } from '../services/mockData'
import { useRealTime } from '../contexts/RealTimeContext'

const TopOnlineUsers = ({ refreshTrigger }) => {
  const { onlineUsers, lastUpdated, isConnected } = useRealTime()
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const users = await fetchTopOnlineUsers()
      setData(users)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Use real-time data if available, otherwise fetch from API
  useEffect(() => {
    if (isConnected && onlineUsers.length > 0) {
      setData(onlineUsers)
      setLoading(false)
      setError(null)
    } else {
      fetchData()
    }
  }, [refreshTrigger, onlineUsers, isConnected])

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'bg-green-500'
      case 'away': return 'bg-yellow-500'
      case 'offline': return 'bg-gray-400'
      default: return 'bg-gray-400'
    }
  }

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return 'Online'
      case 'away': return 'Away'
      case 'offline': return 'Offline'
      default: return 'Unknown'
    }
  }

  const getActivityIcon = (activity) => {
    switch (activity) {
      case 'Trading': return '📈'
      case 'Monitoring': return '👀'
      case 'Idle': return '💤'
      default: return '❓'
    }
  }

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Online Users
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading users...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Online Users
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12 text-red-600 dark:text-red-400">
            <AlertCircle className="h-8 w-8 mr-2" />
            <div className="text-center">
              <p className="font-medium">Failed to load users</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
              <button
                onClick={fetchData}
                className="btn-primary mt-3 text-sm"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-blue-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Online Users
            </h2>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            By Online Time
          </span>
        </div>
      </div>
      <div className="card-content">
        <div className="space-y-3">
          {data.map((user) => (
            <div
              key={user.id || user.rank}
              className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-dark-700/50 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
            >
              {/* Left side: Rank, Name and Status */}
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center text-sm font-bold text-blue-600 dark:text-blue-400">
                  {user.rank}
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {user.name}
                    </p>
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(user.status)}`}></div>
                  </div>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {getStatusText(user.status)} • {user.activity}
                  </p>
                </div>
              </div>

              {/* Right side: Online Time */}
              <div className="text-right">
                <div className="flex items-center space-x-1 text-sm text-gray-900 dark:text-white">
                  <Clock className="h-4 w-4" />
                  <span className="font-medium">{formatTime(user.onlineTime)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No online users found</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default TopOnlineUsers
