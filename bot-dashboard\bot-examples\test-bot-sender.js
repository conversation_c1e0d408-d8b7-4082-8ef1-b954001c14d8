// Test script to simulate bot sending data to dashboard via webhooks
// Run this script to see real-time updates in the dashboard

import fetch from 'node-fetch'

const WEBHOOK_BASE_URL = 'http://localhost:3001/webhook'

// Generate random data for testing
function generateRandomRankings() {
  const names = [
    'CryptoKing', 'TradeBot', 'DiamondHands', 'MoonShot', 'HODLer',
    'AlgoTrader', 'CoinMaster', 'BlockChainer', 'DeFiGuru', 'NFTCollector'
  ]
  
  return names.map((name, index) => ({
    rank: index + 1,
    id: index + 1,
    name,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
    earnings: Math.floor(Math.random() * 100000) + 50000,
    change: Math.floor(Math.random() * 10000) - 5000
  })).sort((a, b) => b.earnings - a.earnings).map((item, index) => ({
    ...item,
    rank: index + 1
  }))
}

function generateRandomOnlineUsers() {
  const names = [
    'CryptoK<PERSON>', 'TradeBot', 'DiamondHands', 'MoonShot', 'HOD<PERSON><PERSON>',
    'AlgoTrader', 'CoinMaster', 'BlockChainer'
  ]
  
  const statuses = ['online', 'away', 'online', 'online', 'away']
  const activities = ['Trading', 'Monitoring', 'Idle']
  
  return names.map((name, index) => ({
    rank: index + 1,
    id: index + 1,
    name,
    avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
    onlineTime: Math.floor(Math.random() * 1440) + 60,
    status: statuses[Math.floor(Math.random() * statuses.length)],
    activity: activities[Math.floor(Math.random() * activities.length)]
  })).sort((a, b) => b.onlineTime - a.onlineTime).map((item, index) => ({
    ...item,
    rank: index + 1
  }))
}

function generateRandomCoinHoldings() {
  const names = [
    'CryptoKing', 'TradeBot', 'DiamondHands', 'MoonShot', 'HODLer',
    'AlgoTrader', 'CoinMaster', 'BlockChainer', 'DeFiGuru', 'NFTCollector'
  ]
  
  const cryptoPrices = { BTC: 43250, ETH: 2680, USDT: 1 }
  
  return names.map((name, index) => {
    const holdings = {
      BTC: Math.random() * 5,
      ETH: Math.random() * 30,
      USDT: Math.random() * 100000
    }
    
    const totalValue = Object.entries(holdings)
      .reduce((sum, [coin, amount]) => sum + (amount * cryptoPrices[coin]), 0)
    
    const topCoin = Object.entries(holdings)
      .sort(([,a], [,b]) => (b * cryptoPrices[Object.keys(cryptoPrices).find(k => k === b)]) - (a * cryptoPrices[Object.keys(cryptoPrices).find(k => k === a)]))
      [0][0]
    
    return {
      rank: index + 1,
      id: index + 1,
      name,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
      holdings,
      totalValue,
      topCoin: 'BTC' // Simplified for demo
    }
  }).sort((a, b) => b.totalValue - a.totalValue).map((item, index) => ({
    ...item,
    rank: index + 1
  }))
}

async function sendWebhookData(endpoint, data) {
  try {
    const response = await fetch(`${WEBHOOK_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()
    console.log(`✅ Webhook data sent to ${endpoint}:`, result.message)
    console.log(`   Processed: ${result.processed} items`)
    return result
  } catch (error) {
    console.error(`❌ Error sending webhook data to ${endpoint}:`, error.message)
    throw error
  }
}

async function updateDashboard() {
  console.log('\n🔄 Updating dashboard with new webhook data...')

  try {
    // Generate random data
    const moneyRankings = generateRandomRankings()
    const onlineUsers = generateRandomOnlineUsers()
    const coinHoldings = generateRandomCoinHoldings()

    // Test different JSON formats
    const testFormats = Math.floor(Math.random() * 3)

    if (testFormats === 0) {
      // Format 1: Send arrays directly
      console.log('📤 Testing format: Direct arrays')
      await Promise.all([
        sendWebhookData('/money-rankings', moneyRankings),
        sendWebhookData('/online-users', onlineUsers),
        sendWebhookData('/coin-holdings', coinHoldings)
      ])
    } else if (testFormats === 1) {
      // Format 2: Send with wrapper objects
      console.log('📤 Testing format: Wrapper objects')
      await Promise.all([
        sendWebhookData('/money-rankings', { rankings: moneyRankings }),
        sendWebhookData('/online-users', { users: onlineUsers }),
        sendWebhookData('/coin-holdings', { holdings: coinHoldings })
      ])
    } else {
      // Format 3: Send all data to universal endpoint
      console.log('📤 Testing format: Universal endpoint')
      await sendWebhookData('/update', {
        money_rankings: moneyRankings,
        online_users: onlineUsers,
        coin_holdings: coinHoldings
      })
    }

    console.log('✅ Dashboard updated successfully via webhooks!')
    console.log(`📊 Sent: ${moneyRankings.length} money rankings, ${onlineUsers.length} online users, ${coinHoldings.length} coin holdings`)

  } catch (error) {
    console.error('❌ Failed to update dashboard:', error.message)
  }
}

async function checkServerHealth() {
  try {
    const response = await fetch('http://localhost:3001/api/health')
    if (response.ok) {
      const health = await response.json()
      console.log('🟢 Webhook server is healthy:', health)
      return true
    }
  } catch (error) {
    console.log('🔴 Webhook server is not responding:', error.message)
    return false
  }
}

async function main() {
  console.log('🤖 Bot Dashboard Webhook Sender - Test Script')
  console.log('==============================================')

  // Check if server is running
  console.log('🔍 Checking webhook server health...')
  const isHealthy = await checkServerHealth()

  if (!isHealthy) {
    console.log('❌ Webhook server is not running. Please start the server first with: npm run server')
    process.exit(1)
  }

  console.log('🚀 Starting to send webhook test data every 15 seconds...')
  console.log('📡 Testing different JSON formats randomly')
  console.log('💡 Open http://localhost:5173 to see the dashboard')
  console.log('📖 Check WEBHOOK_DOCUMENTATION.md for API details')
  console.log('⏹️  Press Ctrl+C to stop\n')

  // Send initial data
  await updateDashboard()

  // Send data every 15 seconds (slower to see different formats)
  const interval = setInterval(updateDashboard, 15000)

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping webhook sender...')
    clearInterval(interval)
    console.log('👋 Goodbye!')
    process.exit(0)
  })
}

// Run the script
main().catch(console.error)
