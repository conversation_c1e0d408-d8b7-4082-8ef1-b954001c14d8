// Example script showing how bot can send data to the dashboard
// This demonstrates the API endpoints that your bot should use

const API_BASE_URL = 'http://localhost:3001/api'

// Example function to send money rankings data
async function sendMoneyRankings(rankings) {
  try {
    const response = await fetch(`${API_BASE_URL}/rankings/money`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        rankings: rankings
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('Money rankings sent successfully:', result)
    return result
  } catch (error) {
    console.error('Error sending money rankings:', error)
    throw error
  }
}

// Example function to send online users data
async function sendOnlineUsers(users) {
  try {
    const response = await fetch(`${API_BASE_URL}/rankings/online`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        users: users
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('Online users sent successfully:', result)
    return result
  } catch (error) {
    console.error('Error sending online users:', error)
    throw error
  }
}

// Example function to send coin holdings data
async function sendCoinHoldings(holdings) {
  try {
    const response = await fetch(`${API_BASE_URL}/rankings/coins`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        holdings: holdings
      })
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const result = await response.json()
    console.log('Coin holdings sent successfully:', result)
    return result
  } catch (error) {
    console.error('Error sending coin holdings:', error)
    throw error
  }
}

// Example data structures that your bot should send:

// Money Rankings Data Structure
const exampleMoneyRankings = [
  {
    rank: 1,
    id: 1,
    name: 'CryptoKing',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoKing',
    earnings: 125000,
    change: 2500 // positive for increase, negative for decrease
  },
  {
    rank: 2,
    id: 2,
    name: 'TradeBot',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TradeBot',
    earnings: 98500,
    change: -1200
  }
  // ... more rankings
]

// Online Users Data Structure
const exampleOnlineUsers = [
  {
    rank: 1,
    id: 1,
    name: 'CryptoKing',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoKing',
    onlineTime: 1440, // minutes
    status: 'online', // 'online', 'away', 'offline'
    activity: 'Trading' // 'Trading', 'Monitoring', 'Idle'
  },
  {
    rank: 2,
    id: 2,
    name: 'TradeBot',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TradeBot',
    onlineTime: 1200,
    status: 'away',
    activity: 'Monitoring'
  }
  // ... more users
]

// Coin Holdings Data Structure
const exampleCoinHoldings = [
  {
    rank: 1,
    id: 1,
    name: 'CryptoKing',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=CryptoKing',
    holdings: {
      BTC: 2.5,
      ETH: 15.2,
      USDT: 50000
    },
    totalValue: 234750, // calculated total value in USD
    topCoin: 'BTC' // coin with highest value
  },
  {
    rank: 2,
    id: 2,
    name: 'TradeBot',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TradeBot',
    holdings: {
      BTC: 1.8,
      ETH: 25.7,
      USDT: 35000
    },
    totalValue: 178650,
    topCoin: 'ETH'
  }
  // ... more holdings
]

// Example usage - how your bot would send data:
async function updateDashboard() {
  try {
    // Send all three types of data
    await sendMoneyRankings(exampleMoneyRankings)
    await sendOnlineUsers(exampleOnlineUsers)
    await sendCoinHoldings(exampleCoinHoldings)
    
    console.log('Dashboard updated successfully!')
  } catch (error) {
    console.error('Failed to update dashboard:', error)
  }
}

// Your bot can call updateDashboard() whenever data changes
// For example, every 30 seconds or when specific events occur

// Example: Update dashboard every 30 seconds
// setInterval(updateDashboard, 30000)

// Export functions for use in your bot
export {
  sendMoneyRankings,
  sendOnlineUsers,
  sendCoinHoldings,
  updateDashboard
}
