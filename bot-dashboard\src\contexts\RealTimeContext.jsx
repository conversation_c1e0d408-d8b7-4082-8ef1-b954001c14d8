import { createContext, useContext, useReducer, useEffect } from 'react'
import { useWebSocket } from '../hooks/useWebSocket'

// Initial state
const initialState = {
  moneyRankings: [],
  onlineUsers: [],
  coinHoldings: [],
  lastUpdated: {
    moneyRankings: null,
    onlineUsers: null,
    coinHoldings: null
  },
  connectionStatus: 'disconnected',
  notifications: []
}

// Action types
const actionTypes = {
  UPDATE_MONEY_RANKINGS: 'UPDATE_MONEY_RANKINGS',
  UPDATE_ONLINE_USERS: 'UPDATE_ONLINE_USERS',
  UPDATE_COIN_HOLDINGS: 'UPDATE_COIN_HOLDINGS',
  SET_CONNECTION_STATUS: 'SET_CONNECTION_STATUS',
  ADD_NOTIFICATION: 'ADD_NOTIFICATION',
  REMOVE_NOTIFICATION: 'REMOVE_NOTIFICATION',
  CLEAR_NOTIFICATIONS: '<PERSON><PERSON><PERSON>_NOTIFICATIONS'
}

// Reducer
const realTimeReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.UPDATE_MONEY_RANKINGS:
      return {
        ...state,
        moneyRankings: action.payload,
        lastUpdated: {
          ...state.lastUpdated,
          moneyRankings: new Date()
        }
      }
      
    case actionTypes.UPDATE_ONLINE_USERS:
      return {
        ...state,
        onlineUsers: action.payload,
        lastUpdated: {
          ...state.lastUpdated,
          onlineUsers: new Date()
        }
      }
      
    case actionTypes.UPDATE_COIN_HOLDINGS:
      return {
        ...state,
        coinHoldings: action.payload,
        lastUpdated: {
          ...state.lastUpdated,
          coinHoldings: new Date()
        }
      }
      
    case actionTypes.SET_CONNECTION_STATUS:
      return {
        ...state,
        connectionStatus: action.payload
      }
      
    case actionTypes.ADD_NOTIFICATION:
      return {
        ...state,
        notifications: [...state.notifications, {
          id: Date.now(),
          ...action.payload,
          timestamp: new Date()
        }]
      }
      
    case actionTypes.REMOVE_NOTIFICATION:
      return {
        ...state,
        notifications: state.notifications.filter(n => n.id !== action.payload)
      }
      
    case actionTypes.CLEAR_NOTIFICATIONS:
      return {
        ...state,
        notifications: []
      }
      
    default:
      return state
  }
}

// Create context
const RealTimeContext = createContext()

// Provider component
export const RealTimeProvider = ({ children }) => {
  const [state, dispatch] = useReducer(realTimeReducer, initialState)
  const { 
    isConnected, 
    connectionStatus, 
    addMessageHandler, 
    sendMessage,
    error 
  } = useWebSocket()

  // Update connection status
  useEffect(() => {
    dispatch({
      type: actionTypes.SET_CONNECTION_STATUS,
      payload: connectionStatus
    })
    
    // Add notification for connection status changes
    if (connectionStatus === 'connected') {
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'success',
          title: 'Connected',
          message: 'Real-time updates enabled'
        }
      })
    } else if (connectionStatus === 'disconnected' || connectionStatus === 'error') {
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'error',
          title: 'Disconnected',
          message: 'Real-time updates disabled'
        }
      })
    } else if (connectionStatus === 'reconnecting') {
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'warning',
          title: 'Reconnecting',
          message: 'Attempting to restore connection...'
        }
      })
    }
  }, [connectionStatus])

  // Set up message handlers
  useEffect(() => {
    // Handle money rankings updates
    const cleanupMoneyHandler = addMessageHandler('money_rankings', (message) => {
      dispatch({
        type: actionTypes.UPDATE_MONEY_RANKINGS,
        payload: message.data
      })
      
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'info',
          title: 'Money Rankings Updated',
          message: `Updated ${message.data.length} rankings`
        }
      })
    })

    // Handle online users updates
    const cleanupOnlineHandler = addMessageHandler('online_users', (message) => {
      dispatch({
        type: actionTypes.UPDATE_ONLINE_USERS,
        payload: message.data
      })
      
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'info',
          title: 'Online Users Updated',
          message: `${message.data.length} users online`
        }
      })
    })

    // Handle coin holdings updates
    const cleanupCoinHandler = addMessageHandler('coin_holdings', (message) => {
      dispatch({
        type: actionTypes.UPDATE_COIN_HOLDINGS,
        payload: message.data
      })
      
      dispatch({
        type: actionTypes.ADD_NOTIFICATION,
        payload: {
          type: 'info',
          title: 'Coin Holdings Updated',
          message: `Updated ${message.data.length} holdings`
        }
      })
    })

    // Cleanup handlers on unmount
    return () => {
      cleanupMoneyHandler()
      cleanupOnlineHandler()
      cleanupCoinHandler()
    }
  }, [addMessageHandler])

  // Auto-remove notifications after 5 seconds
  useEffect(() => {
    const timeouts = state.notifications.map(notification => {
      return setTimeout(() => {
        dispatch({
          type: actionTypes.REMOVE_NOTIFICATION,
          payload: notification.id
        })
      }, 5000)
    })

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout))
    }
  }, [state.notifications])

  // Context value
  const value = {
    ...state,
    isConnected,
    error,
    actions: {
      removeNotification: (id) => dispatch({
        type: actionTypes.REMOVE_NOTIFICATION,
        payload: id
      }),
      clearNotifications: () => dispatch({
        type: actionTypes.CLEAR_NOTIFICATIONS
      }),
      sendMessage
    }
  }

  return (
    <RealTimeContext.Provider value={value}>
      {children}
    </RealTimeContext.Provider>
  )
}

// Hook to use the context
export const useRealTime = () => {
  const context = useContext(RealTimeContext)
  if (!context) {
    throw new Error('useRealTime must be used within a RealTimeProvider')
  }
  return context
}
