// Mock data service for the bot dashboard
// In a real application, these would be API calls

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Mock data generators
const generateUser = (id, name, earnings, isOnline, onlineTime, coinHoldings) => ({
  id,
  name,
  avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`,
  earnings,
  isOnline,
  onlineTime,
  coinHoldings,
  lastSeen: isOnline ? null : new Date(Date.now() - Math.random() * 86400000 * 7), // Random within last week
  status: isOnline ? (Math.random() > 0.7 ? 'away' : 'online') : 'offline'
});

// Mock users data
const mockUsers = [
  generateUser(1, 'CryptoKing', 125000, true, 1440, { BTC: 2.5, ETH: 15.2, USDT: 50000 }),
  generateUser(2, 'TradeBot', 98500, true, 1200, { BTC: 1.8, ETH: 25.7, USDT: 35000 }),
  generateUser(3, 'DiamondHands', 87300, false, 980, { BTC: 3.2, ETH: 8.9, USDT: 75000 }),
  generateUser(4, 'MoonShot', 76200, true, 1380, { BTC: 1.1, ETH: 32.4, USDT: 28000 }),
  generateUser(5, 'HODLer', 65400, true, 720, { BTC: 4.7, ETH: 5.3, USDT: 15000 }),
  generateUser(6, 'AlgoTrader', 58900, false, 1560, { BTC: 0.9, ETH: 18.6, USDT: 42000 }),
  generateUser(7, 'CoinMaster', 52100, true, 840, { BTC: 2.1, ETH: 12.8, USDT: 31000 }),
  generateUser(8, 'BlockChainer', 47800, true, 1100, { BTC: 1.6, ETH: 22.1, USDT: 19000 }),
  generateUser(9, 'DeFiGuru', 43200, false, 660, { BTC: 3.8, ETH: 7.4, USDT: 38000 }),
  generateUser(10, 'NFTCollector', 39500, true, 920, { BTC: 0.7, ETH: 28.3, USDT: 22000 }),
];

// Cryptocurrency prices (mock)
const cryptoPrices = {
  BTC: 43250,
  ETH: 2680,
  USDT: 1
};

// API functions
export const fetchTopMoneyRankings = async () => {
  await delay(800); // Simulate API delay
  
  // Randomly fail sometimes to test error handling
  if (Math.random() < 0.1) {
    throw new Error('Failed to fetch money rankings');
  }
  
  return mockUsers
    .sort((a, b) => b.earnings - a.earnings)
    .slice(0, 10)
    .map((user, index) => ({
      rank: index + 1,
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      earnings: user.earnings,
      change: Math.random() > 0.5 ? Math.floor(Math.random() * 5000) : -Math.floor(Math.random() * 2000)
    }));
};

export const fetchTopOnlineUsers = async () => {
  await delay(600);
  
  if (Math.random() < 0.1) {
    throw new Error('Failed to fetch online users');
  }
  
  return mockUsers
    .filter(user => user.isOnline)
    .sort((a, b) => b.onlineTime - a.onlineTime)
    .slice(0, 10)
    .map((user, index) => ({
      rank: index + 1,
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      onlineTime: user.onlineTime,
      status: user.status,
      activity: Math.random() > 0.5 ? 'Trading' : Math.random() > 0.5 ? 'Monitoring' : 'Idle'
    }));
};

export const fetchTopCoinHoldings = async () => {
  await delay(700);
  
  if (Math.random() < 0.1) {
    throw new Error('Failed to fetch coin holdings');
  }
  
  return mockUsers
    .map(user => {
      const totalValue = Object.entries(user.coinHoldings)
        .reduce((sum, [coin, amount]) => sum + (amount * cryptoPrices[coin]), 0);
      return {
        ...user,
        totalValue
      };
    })
    .sort((a, b) => b.totalValue - a.totalValue)
    .slice(0, 10)
    .map((user, index) => ({
      rank: index + 1,
      id: user.id,
      name: user.name,
      avatar: user.avatar,
      holdings: user.coinHoldings,
      totalValue: user.totalValue,
      topCoin: Object.entries(user.coinHoldings)
        .sort(([,a], [,b]) => (b * cryptoPrices[Object.keys(cryptoPrices).find(k => k === Object.keys(user.coinHoldings).find(c => c === k))]) - (a * cryptoPrices[Object.keys(cryptoPrices).find(k => k === Object.keys(user.coinHoldings).find(c => c === k))]))
        [0][0]
    }));
};

export const getCryptoPrices = () => cryptoPrices;

// Utility functions
export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours}h ${mins}m`;
};

export const formatCrypto = (amount, symbol) => {
  return `${amount.toFixed(symbol === 'BTC' ? 4 : symbol === 'ETH' ? 2 : 0)} ${symbol}`;
};
