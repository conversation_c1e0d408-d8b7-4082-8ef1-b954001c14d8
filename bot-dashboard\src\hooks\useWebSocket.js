import { useState, useEffect, useRef, useCallback } from 'react'

const WEBSOCKET_URL = 'ws://localhost:3001/ws'
const RECONNECT_INTERVAL = 3000
const MAX_RECONNECT_ATTEMPTS = 5

export const useWebSocket = () => {
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState('disconnected')
  const [lastMessage, setLastMessage] = useState(null)
  const [error, setError] = useState(null)
  
  const ws = useRef(null)
  const reconnectAttempts = useRef(0)
  const reconnectTimeout = useRef(null)
  const messageHandlers = useRef(new Map())

  // Connect to WebSocket
  const connect = useCallback(() => {
    try {
      setConnectionStatus('connecting')
      setError(null)
      
      ws.current = new WebSocket(WEBSOCKET_URL)
      
      ws.current.onopen = () => {
        console.log('WebSocket connected')
        setIsConnected(true)
        setConnectionStatus('connected')
        reconnectAttempts.current = 0
        
        // Subscribe to all data types
        ws.current.send(JSON.stringify({
          type: 'subscribe',
          subscriptions: ['all']
        }))
      }
      
      ws.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          setLastMessage(message)
          
          // Call specific handlers for message types
          const handler = messageHandlers.current.get(message.type)
          if (handler) {
            handler(message)
          }
          
          // Call general message handler
          const generalHandler = messageHandlers.current.get('*')
          if (generalHandler) {
            generalHandler(message)
          }
          
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }
      
      ws.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason)
        setIsConnected(false)
        setConnectionStatus('disconnected')
        
        // Attempt to reconnect if not manually closed
        if (event.code !== 1000 && reconnectAttempts.current < MAX_RECONNECT_ATTEMPTS) {
          setConnectionStatus('reconnecting')
          reconnectTimeout.current = setTimeout(() => {
            reconnectAttempts.current++
            connect()
          }, RECONNECT_INTERVAL)
        } else if (reconnectAttempts.current >= MAX_RECONNECT_ATTEMPTS) {
          setError('Max reconnection attempts reached')
          setConnectionStatus('failed')
        }
      }
      
      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        setError('WebSocket connection error')
        setConnectionStatus('error')
      }
      
    } catch (error) {
      console.error('Error creating WebSocket connection:', error)
      setError('Failed to create WebSocket connection')
      setConnectionStatus('error')
    }
  }, [])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeout.current) {
      clearTimeout(reconnectTimeout.current)
    }
    
    if (ws.current) {
      ws.current.close(1000, 'Manual disconnect')
    }
    
    setIsConnected(false)
    setConnectionStatus('disconnected')
    reconnectAttempts.current = 0
  }, [])

  // Send message to WebSocket
  const sendMessage = useCallback((message) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(message))
      return true
    }
    return false
  }, [])

  // Add message handler for specific message type
  const addMessageHandler = useCallback((type, handler) => {
    messageHandlers.current.set(type, handler)
    
    // Return cleanup function
    return () => {
      messageHandlers.current.delete(type)
    }
  }, [])

  // Send ping to keep connection alive
  const ping = useCallback(() => {
    return sendMessage({ type: 'ping' })
  }, [sendMessage])

  // Initialize connection on mount
  useEffect(() => {
    connect()
    
    // Set up ping interval to keep connection alive
    const pingInterval = setInterval(() => {
      if (isConnected) {
        ping()
      }
    }, 30000) // Ping every 30 seconds
    
    // Cleanup on unmount
    return () => {
      clearInterval(pingInterval)
      if (reconnectTimeout.current) {
        clearTimeout(reconnectTimeout.current)
      }
      disconnect()
    }
  }, [connect, disconnect, ping, isConnected])

  return {
    isConnected,
    connectionStatus,
    lastMessage,
    error,
    sendMessage,
    addMessageHandler,
    connect,
    disconnect,
    ping
  }
}
