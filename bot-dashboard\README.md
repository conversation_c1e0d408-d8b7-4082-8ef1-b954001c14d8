# Bot Dashboard - Real-time Webhook Dashboard

Dashboard web real-time để hiển thị thống kê bot với 3 bảng xếp hạng chính:
- **Top Money Rankings** - Xếp hạng theo thu nhập/tiền tệ
- **Top Online Users** - Xếp hạng users đang online
- **Top Coin Holdings** - Xếp hạng theo holdings cryptocurrency

## Tính năng

✅ **Real-time Updates** - Cập nhật tức thì qua WebSocket
✅ **Webhook Integration** - Bot gửi JSON data vào webhook endpoints
✅ **Flexible JSON Format** - Hỗ trợ nhiều format JSON khác nhau
✅ **Responsive Design** - Giao diện responsive, hỗ trợ mobile
✅ **Dark/Light Mode** - Chế độ sáng/tối
✅ **Auto Refresh** - Tự động refresh với interval tùy chỉnh
✅ **Error Handling** - Xử lý lỗi và loading states
✅ **Toast Notifications** - Thông báo real-time updates

## Cài đặt và Chạy

```bash
# Cài đặt dependencies
npm install

# Chạy full stack (server + client)
npm run dev:full
```

- **Dashboard:** http://localhost:5173
- **Webhook Server:** http://localhost:3001
- **Health Check:** http://localhost:3001/api/health

## Webhook Endpoints cho Bot

Bot của bạn có thể POST JSON data vào các endpoints sau:

### 1. Money Rankings
```bash
POST http://localhost:3001/webhook/money-rankings
```

### 2. Online Users
```bash
POST http://localhost:3001/webhook/online-users
```

### 3. Coin Holdings
```bash
POST http://localhost:3001/webhook/coin-holdings
```

### 4. Universal Endpoint (tất cả data)
```bash
POST http://localhost:3001/webhook/update
```

## Ví dụ JSON Format

Bot có thể gửi JSON theo nhiều format khác nhau:

**Money Rankings:**
```json
[
  {"name": "User1", "earnings": 100000, "change": 5000},
  {"name": "User2", "earnings": 95000, "change": -2000}
]
```

**Online Users:**
```json
{
  "users": [
    {"username": "User1", "onlineTime": 120, "status": "online"},
    {"username": "User2", "onlineTime": 90, "status": "away"}
  ]
}
```

**Coin Holdings:**
```json
{
  "holdings": [
    {
      "name": "User1",
      "BTC": 1.5,
      "ETH": 10.2,
      "USDT": 25000
    }
  ]
}
```

## Test Dashboard

```bash
# 1. Start server và client
npm run dev:full

# 2. Chạy test bot (terminal mới)
npm run test-bot

# 3. Mở http://localhost:5173 để xem dashboard
```

## Scripts

- `npm run dev:full` - Chạy cả server và client
- `npm run server` - Chỉ chạy webhook server
- `npm run dev` - Chỉ chạy React client
- `npm run test-bot` - Test gửi data đến webhook

## Documentation

Xem [WEBHOOK_DOCUMENTATION.md](./WEBHOOK_DOCUMENTATION.md) để biết chi tiết về:
- Tất cả webhook endpoints
- Các format JSON được hỗ trợ
- Ví dụ integration với Python, JavaScript, cURL
- Response format và error handling
