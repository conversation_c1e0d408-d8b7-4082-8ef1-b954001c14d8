import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, DollarSign, AlertCircle, Loader2 } from 'lucide-react'
import { fetchTopMoneyRankings, formatCurrency } from '../services/mockData'
import { useRealTime } from '../contexts/RealTimeContext'

const TopMoneyRankings = ({ refreshTrigger }) => {
  const { moneyRankings, lastUpdated, isConnected } = useRealTime()
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const rankings = await fetchTopMoneyRankings()
      setData(rankings)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Use real-time data if available, otherwise fetch from API
  useEffect(() => {
    if (isConnected && moneyRankings.length > 0) {
      setData(moneyRankings)
      setLoading(false)
      setError(null)
    } else {
      fetchData()
    }
  }, [refreshTrigger, moneyRankings, isConnected])

  const getRankBadgeColor = (rank) => {
    switch (rank) {
      case 1: return 'bg-yellow-500 text-white'
      case 2: return 'bg-gray-400 text-white'
      case 3: return 'bg-amber-600 text-white'
      default: return 'bg-gray-200 dark:bg-dark-700 text-gray-700 dark:text-gray-300'
    }
  }

  const getChangeIcon = (change) => {
    if (change > 0) return <TrendingUp className="h-4 w-4 text-green-500" />
    if (change < 0) return <TrendingDown className="h-4 w-4 text-red-500" />
    return null
  }

  const getChangeColor = (change) => {
    if (change > 0) return 'text-green-600 dark:text-green-400'
    if (change < 0) return 'text-red-600 dark:text-red-400'
    return 'text-gray-500 dark:text-gray-400'
  }

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Money Rankings
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading rankings...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Money Rankings
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12 text-red-600 dark:text-red-400">
            <AlertCircle className="h-8 w-8 mr-2" />
            <div className="text-center">
              <p className="font-medium">Failed to load rankings</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
              <button
                onClick={fetchData}
                className="btn-primary mt-3 text-sm"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <DollarSign className="h-5 w-5 text-green-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Money Rankings
            </h2>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Total Earnings
          </span>
        </div>
      </div>
      <div className="card-content">
        <div className="space-y-3">
          {data.map((user) => (
            <div
              key={user.id || user.rank}
              className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-dark-700/50 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
            >
              {/* Left side: Rank and Name */}
              <div className="flex items-center space-x-3">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${getRankBadgeColor(user.rank)}`}>
                  {user.rank}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {user.name}
                  </p>
                </div>
              </div>

              {/* Right side: Earnings and Change */}
              <div className="text-right">
                <p className="text-sm font-semibold text-gray-900 dark:text-white">
                  {formatCurrency(user.earnings)}
                </p>
                {user.change !== undefined && user.change !== 0 && (
                  <div className={`flex items-center justify-end space-x-1 text-xs ${getChangeColor(user.change)}`}>
                    {getChangeIcon(user.change)}
                    <span>
                      {user.change > 0 ? '+' : ''}{formatCurrency(user.change)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <DollarSign className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No earnings data available</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default TopMoneyRankings
