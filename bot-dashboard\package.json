{"name": "bot-dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/websocket-server.js", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "test-bot": "node bot-examples/test-bot-sender.js", "start": "concurrently \"npm run server\" \"npm run dev\" \"npm run test-bot\""}, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "autoprefixer": "^10.4.21", "cors": "^2.8.5", "express": "^5.1.0", "lucide-react": "^0.525.0", "node-fetch": "^3.3.2", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "^3.1.0", "tailwindcss": "^4.1.11", "ws": "^8.18.3"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "vite": "^7.0.4"}}