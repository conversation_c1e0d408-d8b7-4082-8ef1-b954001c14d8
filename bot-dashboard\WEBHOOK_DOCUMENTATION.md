# Bot Dashboard Webhook Documentation

## Tổng quan
Dashboard này cung cấp các webhook endpoints để bot có thể gửi dữ liệu JSON và cập nhật dashboard real-time.

## Base URL
```
http://localhost:3001
```

## Webhook Endpoints

### 1. Money Rankings Webhook
**Endpoint:** `POST /webhook/money-rankings`

Bot gửi dữ liệu xếp hạng tiền tệ/thu nhập của users.

#### Các format JSON được hỗ trợ:

**Format 1: Array trực tiếp**
```json
[
  {
    "name": "CryptoKing",
    "earnings": 125000,
    "change": 2500
  },
  {
    "name": "TradeBot", 
    "earnings": 98500,
    "change": -1200
  }
]
```

**Format 2: Object với key "rankings"**
```json
{
  "rankings": [
    {
      "id": 1,
      "username": "CryptoKing",
      "money": 125000,
      "diff": 2500,
      "avatar": "https://example.com/avatar1.jpg"
    }
  ]
}
```

**Format 3: Object với key "data" hoặc "users"**
```json
{
  "data": [
    {
      "user_id": 1,
      "user": "CryptoKing",
      "balance": 125000,
      "delta": 2500
    }
  ]
}
```

#### Các field được hỗ trợ:
- `name` / `username` / `user` - Tên user
- `earnings` / `money` / `balance` / `total` - Số tiền
- `change` / `diff` / `delta` - Thay đổi so với lần trước
- `id` / `user_id` / `userId` - ID của user
- `avatar` / `profile_image` / `image` - URL avatar
- `rank` - Thứ hạng (tự động tính nếu không có)

---

### 2. Online Users Webhook
**Endpoint:** `POST /webhook/online-users`

Bot gửi dữ liệu users đang online.

#### Các format JSON được hỗ trợ:

**Format 1: Array trực tiếp**
```json
[
  {
    "name": "CryptoKing",
    "onlineTime": 1440,
    "status": "online",
    "activity": "Trading"
  }
]
```

**Format 2: Object với key "users" hoặc "online"**
```json
{
  "users": [
    {
      "username": "CryptoKing",
      "online_time": 1440,
      "online": true,
      "doing": "Trading"
    }
  ]
}
```

#### Các field được hỗ trợ:
- `name` / `username` / `user` - Tên user
- `onlineTime` / `online_time` / `time` / `minutes` - Thời gian online (phút)
- `status` - Trạng thái: "online", "away", "offline"
- `activity` / `action` / `doing` - Hoạt động hiện tại
- `online` - Boolean, true nếu đang online

---

### 3. Coin Holdings Webhook
**Endpoint:** `POST /webhook/coin-holdings`

Bot gửi dữ liệu holdings cryptocurrency của users.

#### Các format JSON được hỗ trợ:

**Format 1: Array với holdings object**
```json
[
  {
    "name": "CryptoKing",
    "holdings": {
      "BTC": 2.5,
      "ETH": 15.2,
      "USDT": 50000
    },
    "totalValue": 234750
  }
]
```

**Format 2: Array với coins trực tiếp**
```json
[
  {
    "username": "CryptoKing",
    "BTC": 2.5,
    "ETH": 15.2,
    "USDT": 50000,
    "total_value": 234750
  }
]
```

**Format 3: Object với key "holdings" hoặc "coins"**
```json
{
  "holdings": [
    {
      "user": "CryptoKing",
      "wallet": {
        "BTC": 2.5,
        "ETH": 15.2,
        "USDT": 50000
      }
    }
  ]
}
```

#### Các field được hỗ trợ:
- `holdings` / `coins` / `wallet` - Object chứa số lượng coins
- `totalValue` / `total_value` / `value` - Tổng giá trị USD
- `topCoin` / `top_coin` - Coin có giá trị cao nhất
- Các coin: `BTC`, `ETH`, `USDT`, `BNB`, `ADA`, `DOT`, `LINK`, `UNI`

---

### 4. Universal Webhook
**Endpoint:** `POST /webhook/update`

Bot có thể gửi tất cả loại dữ liệu trong 1 request.

```json
{
  "money_rankings": [
    {"name": "User1", "earnings": 100000}
  ],
  "online_users": [
    {"name": "User1", "onlineTime": 120, "status": "online"}
  ],
  "coin_holdings": [
    {"name": "User1", "BTC": 1.5, "ETH": 10}
  ]
}
```

## Response Format

Tất cả webhook endpoints trả về JSON response:

**Success Response:**
```json
{
  "success": true,
  "message": "Data updated successfully",
  "processed": 5,
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Error Response:**
```json
{
  "error": "Invalid JSON data",
  "message": "Expected JSON object"
}
```

## Cách sử dụng

### Python Example
```python
import requests
import json

# Gửi money rankings
data = [
    {"name": "User1", "earnings": 100000, "change": 5000},
    {"name": "User2", "earnings": 95000, "change": -2000}
]

response = requests.post(
    'http://localhost:3001/webhook/money-rankings',
    json=data,
    headers={'Content-Type': 'application/json'}
)

print(response.json())
```

### JavaScript/Node.js Example
```javascript
const axios = require('axios');

const data = {
  users: [
    {name: "User1", onlineTime: 120, status: "online"},
    {name: "User2", onlineTime: 90, status: "away"}
  ]
};

axios.post('http://localhost:3001/webhook/online-users', data)
  .then(response => console.log(response.data))
  .catch(error => console.error(error));
```

### cURL Example
```bash
curl -X POST http://localhost:3001/webhook/coin-holdings \
  -H "Content-Type: application/json" \
  -d '[
    {
      "name": "User1",
      "BTC": 1.5,
      "ETH": 10.2,
      "USDT": 25000
    }
  ]'
```

## Testing

Để test webhook, bạn có thể:

1. Start server: `npm run server`
2. Start dashboard: `npm run dev` 
3. Gửi test data bằng script: `npm run test-bot`

Hoặc sử dụng Postman/curl để gửi JSON data đến các endpoints trên.

## Notes

- Server tự động chuyển đổi các format JSON khác nhau về format chuẩn
- Nếu thiếu field nào, server sẽ tự động tạo giá trị mặc định
- Dashboard sẽ cập nhật real-time khi nhận được data từ webhook
- Tất cả timestamps được tự động thêm vào
- Server log tất cả requests để debug
