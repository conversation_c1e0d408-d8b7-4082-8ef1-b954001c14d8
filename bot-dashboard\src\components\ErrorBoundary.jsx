import { Component } from 'react'
import { AlertTriangle, RefreshCw } from 'lucide-react'

class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    this.setState({
      error: error,
      errorInfo: errorInfo
    })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="card">
          <div className="card-content">
            <div className="flex items-center justify-center py-12 text-red-600 dark:text-red-400">
              <AlertTriangle className="h-8 w-8 mr-2" />
              <div className="text-center">
                <h3 className="font-medium text-lg mb-2">Something went wrong</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                  {this.state.error && this.state.error.toString()}
                </p>
                <button
                  onClick={this.handleReset}
                  className="btn-primary flex items-center space-x-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  <span>Try Again</span>
                </button>
                {process.env.NODE_ENV === 'development' && (
                  <details className="mt-4 text-left">
                    <summary className="cursor-pointer text-sm text-gray-600 dark:text-gray-400">
                      Error Details (Development)
                    </summary>
                    <pre className="mt-2 text-xs bg-gray-100 dark:bg-dark-800 p-2 rounded overflow-auto">
                      {this.state.error && this.state.error.stack}
                      {this.state.errorInfo.componentStack}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
