import { useState, useEffect } from 'react'
import { RefreshCw, TrendingUp, Users, Co<PERSON>, Moon, Sun } from 'lucide-react'
import TopMoneyRankings from './components/TopMoneyRankings'
import TopOnlineUsers from './components/TopOnlineUsers'
import TopCoinHoldings from './components/TopCoinHoldings'
import ErrorBoundary from './components/ErrorBoundary'
import NotificationToast from './components/NotificationToast'
import { RealTimeProvider, useRealTime } from './contexts/RealTimeContext'

const DashboardContent = () => {
  const { isConnected, lastUpdated } = useRealTime()
  const [darkMode, setDarkMode] = useState(false)
  const [lastRefresh, setLastRefresh] = useState(new Date())
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [refreshInterval, setRefreshInterval] = useState(30) // seconds

  // Auto-refresh functionality
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(() => {
      setLastRefresh(new Date())
    }, refreshInterval * 1000)

    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval])

  // Dark mode toggle
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }, [darkMode])

  const handleManualRefresh = () => {
    setLastRefresh(new Date())
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-dark-900 transition-colors duration-200">
      {/* Header */}
      <header className="bg-white dark:bg-dark-800 shadow-sm border-b border-gray-200 dark:border-dark-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-primary-600" />
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Bot Dashboard
                </h1>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Auto-refresh controls */}
              <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <label className="flex items-center space-x-1">
                  <input
                    type="checkbox"
                    checked={autoRefresh}
                    onChange={(e) => setAutoRefresh(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span>Auto-refresh</span>
                </label>
                <select
                  value={refreshInterval}
                  onChange={(e) => setRefreshInterval(Number(e.target.value))}
                  disabled={!autoRefresh}
                  className="rounded border-gray-300 dark:border-dark-600 bg-white dark:bg-dark-700 text-gray-900 dark:text-gray-100 text-sm"
                >
                  <option value={15}>15s</option>
                  <option value={30}>30s</option>
                  <option value={60}>1m</option>
                  <option value={300}>5m</option>
                </select>
              </div>

              {/* Manual refresh button */}
              <button
                onClick={handleManualRefresh}
                className="btn-secondary flex items-center space-x-1"
                title="Manual refresh"
              >
                <RefreshCw className="h-4 w-4" />
                <span className="hidden sm:inline">Refresh</span>
              </button>

              {/* Dark mode toggle */}
              <button
                onClick={() => setDarkMode(!darkMode)}
                className="btn-secondary p-2"
                title="Toggle dark mode"
              >
                {darkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Last Updated
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {lastRefresh.toLocaleTimeString()}
                  </p>
                </div>
                <RefreshCw className="h-8 w-8 text-primary-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Auto Refresh
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {autoRefresh ? `${refreshInterval}s` : 'Off'}
                  </p>
                </div>
                <Users className="h-8 w-8 text-green-600" />
              </div>
            </div>
          </div>

          <div className="card">
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                    Theme
                  </p>
                  <p className="text-lg font-semibold text-gray-900 dark:text-white">
                    {darkMode ? 'Dark' : 'Light'}
                  </p>
                </div>
                <Coins className="h-8 w-8 text-yellow-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard sections */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          <ErrorBoundary>
            <TopMoneyRankings refreshTrigger={lastRefresh} />
          </ErrorBoundary>
          <ErrorBoundary>
            <TopOnlineUsers refreshTrigger={lastRefresh} />
          </ErrorBoundary>
          <ErrorBoundary>
            <TopCoinHoldings refreshTrigger={lastRefresh} />
          </ErrorBoundary>
        </div>
      </main>

      {/* Real-time notifications */}
      <NotificationToast />
    </div>
  )
}

function App() {
  return (
    <RealTimeProvider>
      <DashboardContent />
    </RealTimeProvider>
  )
}

export default App
