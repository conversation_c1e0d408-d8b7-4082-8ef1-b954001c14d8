import { useState, useEffect } from 'react'
import { Coins, TrendingUp, AlertCircle, Loader2 } from 'lucide-react'
import { fetchTopCoinHoldings, formatCurrency, formatCrypto, getCryptoPrices } from '../services/mockData'
import { useRealTime } from '../contexts/RealTimeContext'

const TopCoinHoldings = ({ refreshTrigger }) => {
  const { coinHoldings, lastUpdated, isConnected } = useRealTime()
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      const holdings = await fetchTopCoinHoldings()
      setData(holdings)
    } catch (err) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  // Use real-time data if available, otherwise fetch from API
  useEffect(() => {
    if (isConnected && coinHoldings.length > 0) {
      setData(coinHoldings)
      setLoading(false)
      setError(null)
    } else {
      fetchData()
    }
  }, [refreshTrigger, coinHoldings, isConnected])

  const getCoinIcon = (coin) => {
    switch (coin) {
      case 'BTC': return '₿'
      case 'ETH': return 'Ξ'
      case 'USDT': return '₮'
      default: return '🪙'
    }
  }

  const getCoinColor = (coin) => {
    switch (coin) {
      case 'BTC': return 'text-orange-500'
      case 'ETH': return 'text-blue-500'
      case 'USDT': return 'text-green-500'
      default: return 'text-gray-500'
    }
  }

  const cryptoPrices = getCryptoPrices()

  if (loading) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <Coins className="h-5 w-5 text-yellow-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Coin Holdings
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading holdings...</span>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="card">
        <div className="card-header">
          <div className="flex items-center space-x-2">
            <Coins className="h-5 w-5 text-yellow-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Coin Holdings
            </h2>
          </div>
        </div>
        <div className="card-content">
          <div className="flex items-center justify-center py-12 text-red-600 dark:text-red-400">
            <AlertCircle className="h-8 w-8 mr-2" />
            <div className="text-center">
              <p className="font-medium">Failed to load holdings</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{error}</p>
              <button
                onClick={fetchData}
                className="btn-primary mt-3 text-sm"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Coins className="h-5 w-5 text-yellow-600" />
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Top Coin Holdings
            </h2>
          </div>
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Total Value
          </span>
        </div>
      </div>
      <div className="card-content">
        <div className="space-y-3">
          {data.map((user) => (
            <div
              key={user.id || user.rank}
              className="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-dark-700/50 hover:bg-gray-100 dark:hover:bg-dark-700 transition-colors"
            >
              {/* Left side: Rank and Name */}
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center text-sm font-bold text-yellow-600 dark:text-yellow-400">
                  {user.rank}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {user.name}
                  </p>
                  {/* Holdings breakdown */}
                  <div className="flex items-center space-x-2 mt-1">
                    {Object.entries(user.holdings || {}).slice(0, 3).map(([coin, amount]) => (
                      <div key={coin} className="flex items-center space-x-1">
                        <span className={`text-sm ${getCoinColor(coin)}`}>
                          {getCoinIcon(coin)}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {formatCrypto(amount, coin)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Right side: Total Value and Top Coin */}
              <div className="text-right">
                <p className="text-sm font-semibold text-gray-900 dark:text-white">
                  {formatCurrency(user.totalValue)}
                </p>
                <div className="flex items-center justify-end space-x-1 mt-1">
                  <span className={`text-lg ${getCoinColor(user.topCoin)}`}>
                    {getCoinIcon(user.topCoin)}
                  </span>
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {user.topCoin}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {data.length === 0 && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            <Coins className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No coin holdings data available</p>
          </div>
        )}

        {/* Price reference */}
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-dark-700">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-2">Current Prices:</p>
          <div className="flex items-center justify-between text-sm">
            {Object.entries(cryptoPrices).map(([coin, price]) => (
              <div key={coin} className="flex items-center space-x-1">
                <span className={`${getCoinColor(coin)}`}>
                  {getCoinIcon(coin)}
                </span>
                <span className="text-gray-600 dark:text-gray-400">
                  {coin}:
                </span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(price)}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TopCoinHoldings
